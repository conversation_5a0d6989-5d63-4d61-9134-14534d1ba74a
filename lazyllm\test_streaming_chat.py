# 测试流式输出的多轮对话系统

import lazyllm
from jina_embedding import create_jina_embedding
from functools import partial

def test_streaming_chat():
    """测试流式输出功能"""
    print("=== 测试流式输出多轮对话 ===")
    
    # 创建简单的LLM实例
    llm = lazyllm.OnlineChatModule(
        source="openai",
        model="ep-20250822223253-s98w6",
        base_url="https://ark.cn-beijing.volces.com/api/v3/",
        api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
        stream=True, return_trace=True
    )
    
    # 设置简单提示词
    llm.prompt(lazyllm.ChatPrompter(instruction="你是一个有用的AI助手"))
    
    chat_history = []
    
    while True:
        query = input("\n用户: ").strip()
        
        if query.lower() in ['quit', 'exit']:
            print("再见！")
            break
        
        if not query:
            continue
            
        print("\n助手: ", end="", flush=True)
        
        try:
            # 构建聊天历史
            llm_chat_history = []
            for user_msg, assistant_msg in chat_history:
                llm_chat_history.append({"role": "user", "content": user_msg})
                llm_chat_history.append({"role": "assistant", "content": assistant_msg})
            
            # 使用流式输出
            with lazyllm.ThreadPoolExecutor(1) as executor:
                future = executor.submit(partial(llm, llm_chat_history=llm_chat_history), query)
                
                complete_response = ""
                
                while True:
                    if value := lazyllm.FileSystemQueue().dequeue():
                        output_text = ''.join(value)
                        print(output_text, end="", flush=True)
                        complete_response += output_text
                    elif future.done():
                        final_result = future.result()
                        break
                
                print()  # 换行
                
                # 保存到历史
                response = final_result if final_result else complete_response
                chat_history.append((query, response))
                
                # 限制历史长度
                if len(chat_history) > 5:
                    chat_history = chat_history[-5:]
                    
        except Exception as e:
            print(f"\n错误: {e}")

if __name__ == "__main__":
    test_streaming_chat()
