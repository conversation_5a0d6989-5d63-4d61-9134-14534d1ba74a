#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jina AI 嵌入模块
提供符合 OpenAI 格式的 Jina AI 嵌入服务
"""

from lazyllm.module import OnlineEmbeddingModuleBase
from typing import Dict, List, Union


class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
    """Jina AI 嵌入模块 - 符合 OpenAI 格式"""
    
    def __init__(self, api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
        """
        初始化 Jina AI 嵌入模块
        
        参数:
            api_key: Jina AI 的 API 密钥
            model_name: 嵌入模型名称，默认为 "jina-embeddings-v3"
            task: 任务类型，默认为 "text-matching"
        """
        # Jina AI 的 API 端点
        embed_url = "https://api.jina.ai/v1/embeddings"
        
        super().__init__(
            model_series="openai",  # 使用 openai 格式
            embed_url=embed_url,
            api_key=api_key,
            embed_model_name=model_name
        )
        self.task = task
    
    def _encapsulated_data(self, text: str, **kwargs) -> Dict:
        """
        封装请求数据为 OpenAI/Jina AI 格式
        
        参数:
            text: 要嵌入的文本
            **kwargs: 其他参数
            
        返回:
            Dict: 格式化的请求数据
        """
        # 如果是单个文本，转换为列表
        if isinstance(text, str):
            input_texts = [text]
        else:
            input_texts = text if isinstance(text, list) else [text]
            
        json_data = {
            "model": self._embed_model_name,
            "input": input_texts,
            "task": self.task  # Jina AI 特有的参数
        }
        
        # 添加其他参数
        if len(kwargs) > 0:
            json_data.update(kwargs)
            
        return json_data
    
    def _parse_response(self, response: Union[List, Dict]) -> Union[List[List[float]], List[float]]:
        """
        解析 OpenAI/Jina AI 格式的响应
        
        参数:
            response: API 响应数据
            
        返回:
            Union[List[List[float]], List[float]]: 嵌入向量
        """
        if isinstance(response, dict) and "data" in response:
            # OpenAI/Jina AI 返回格式: {"data": [{"embedding": [...]}, ...]}
            embeddings = [item["embedding"] for item in response["data"]]
            return embeddings[0] if len(embeddings) == 1 else embeddings
        else:
            raise ValueError(f"无法解析响应格式: {response}")


def create_jina_embedding(api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
    """
    创建 Jina AI 嵌入模块的工厂函数
    
    参数:
        api_key: Jina AI 的 API 密钥
        model_name: 嵌入模型名称，默认为 "jina-embeddings-v3"
        task: 任务类型，默认为 "text-matching"
        
    返回:
        JinaEmbeddingModule: 配置好的 Jina AI 嵌入模块实例
    """
    return JinaEmbeddingModule(
        api_key=api_key,
        model_name=model_name,
        task=task
    )
