# Part0

import lazyllm
from jina_embedding import create_jina_embedding
from functools import partial

# 创建 Jina AI 嵌入模块实例
jina_embedding = create_jina_embedding(
    api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
    model_name="jina-embeddings-v3",
    task="text-matching"
)

# Part1

documents = lazyllm.Document(dataset_path="datas",
                             embed=jina_embedding,
                             manager=False)

# Part2

retriever = lazyllm.Retriever(doc=documents,
                              group_name="CoarseChunk",
                              similarity="bm25_chinese",
                              topk=3)

# Part3


llm = lazyllm.OnlineChatModule(
    source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=True, return_trace=True
    ) 

# Part4
prompt = '''你将扮演一个人工智能问答助手的角色，完成一项多轮对话任务。在这个任务中，你需要根据给定的上下文、对话历史以及当前问题，给出你的回答。

请注意：
1. 结合对话历史理解当前问题的完整含义
2. 基于提供的上下文信息回答问题
3. 保持回答的连贯性和一致性
4. 如果当前问题与之前的对话相关，请适当引用历史信息
'''
llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str', 'chat_history']))

# Part5 - 多轮对话主循环

def formatChatHistory(chat_history):
    """格式化对话历史为字符串"""
    if not chat_history:
        return "无对话历史"

    formatted_history = []
    for i, (user_msg, assistant_msg) in enumerate(chat_history, 1):
        formatted_history.append(f"第{i}轮对话:")
        formatted_history.append(f"用户: {user_msg}")
        formatted_history.append(f"助手: {assistant_msg}")
        formatted_history.append("")  # 空行分隔

    return "\n".join(formatted_history)

def runMultiTurnChat():
    """运行多轮对话（支持流式输出）"""
    chat_history = []  # 存储对话历史 [(user_msg, assistant_msg), ...]

    print("=== 多轮对话 RAG 系统（流式输出）===")
    print("输入 'quit' 或 'exit' 退出对话")
    print("输入 'clear' 清空对话历史")
    print("输入 'history' 查看对话历史")
    print("-" * 50)

    while True:
        try:
            # 获取用户输入
            query = input("\n用户: ").strip()

            # 处理特殊命令
            if query.lower() in ['quit', 'exit']:
                print("再见！")
                break
            elif query.lower() == 'clear':
                chat_history.clear()
                print("对话历史已清空")
                continue
            elif query.lower() == 'history':
                if chat_history:
                    print("\n=== 对话历史 ===")
                    print(formatChatHistory(chat_history))
                else:
                    print("暂无对话历史")
                continue
            elif not query:
                print("请输入有效的问题")
                continue

            # 检索相关文档
            print("正在检索相关信息...")
            doc_node_list = retriever(query=query)
            context_str = "".join([node.get_content() for node in doc_node_list])

            # 格式化对话历史
            history_str = formatChatHistory(chat_history)

            # 准备LLM输入参数
            llm_input = {
                "query": query,
                "context_str": context_str,
                "chat_history": history_str
            }

            # 使用流式输出生成回答
            print("\n助手: ", end="", flush=True)
            complete_response = generateStreamingResponse(llm_input, chat_history)

            # 将当前对话添加到历史记录
            chat_history.append((query, complete_response))

            # 限制历史记录长度，避免上下文过长
            if len(chat_history) > 10:  # 保留最近10轮对话
                chat_history = chat_history[-10:]

        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")
            print("请重试或输入 'quit' 退出")

def generateStreamingResponse(llm_input, current_chat_history):
    """生成流式输出响应"""
    try:
        # 构建LLM聊天历史格式 - 简化版本，直接传递查询
        query = llm_input["query"]

        # 使用ThreadPoolExecutor进行异步处理
        with lazyllm.ThreadPoolExecutor(1) as executor:
            # 提交LLM任务，传递完整的llm_input和空的聊天历史
            future = executor.submit(partial(llm, llm_chat_history=[]), llm_input)

            complete_response = ""

            # 处理流式输出
            while True:
                # 从队列中获取流式输出
                if value := lazyllm.FileSystemQueue().dequeue():
                    output_text = ''.join(value)
                    print(output_text, end="", flush=True)
                    complete_response += output_text
                elif future.done():
                    # 任务完成，获取最终结果
                    final_result = future.result()
                    break

            print()  # 换行

            # 返回最终结果或完整响应
            return final_result if final_result else complete_response

    except Exception as e:
        print(f"\n流式输出生成失败: {e}")
        # 降级到普通模式
        try:
            return llm(llm_input)
        except Exception as fallback_error:
            print(f"普通模式也失败: {fallback_error}")
            return "抱歉，生成回答时出现错误。"

# Part6 - 启动多轮对话

if __name__ == "__main__":
    runMultiTurnChat()