# Part0

import lazyllm
from jina_embedding import create_jina_embedding

# 创建 Jina AI 嵌入模块实例
jina_embedding = create_jina_embedding(
    api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
    model_name="jina-embeddings-v3",
    task="text-matching"
)

# Part1

documents = lazyllm.Document(dataset_path="datas",
                             embed=jina_embedding,
                             manager=False)

# Part2

retriever = lazyllm.Retriever(doc=documents,
                              group_name="CoarseChunk",
                              similarity="bm25_chinese",
                              topk=3)

# Part3


llm = lazyllm.OnlineChatModule(
    source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=True, return_trace=True
    ) 

# Part4

prompt = '你将扮演一个人工智能问答助手的角色，完成一项对话任务。在这个任务中，你需要根据给定的上下文以及问题，给出你的回答。'
llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))

# Part5

import sys

def get_user_input():
    """获取用户输入，支持交互式和非交互式环境"""
    try:
        # 检查是否在交互式环境中
        if sys.stdin.isatty():
            # 交互式环境
            while True:
                query = input("query(enter 'quit' to exit): ").strip()
                if query.lower() == "quit":
                    print("👋 再见！")
                    exit(0)
                elif query:
                    return query
                else:
                    print("⚠️  请输入有效的查询内容")
        else:
            # 非交互式环境，使用默认测试查询
            test_queries = [
                "什么是人工智能？",
                "如何使用机器学习？",
                "深度学习的应用场景有哪些？"
            ]
            print("🤖 检测到非交互式环境，使用测试查询...")
            for i, test_query in enumerate(test_queries, 1):
                print(f"\n📝 测试查询 {i}: {test_query}")
                return test_query
    except (EOFError, KeyboardInterrupt):
        print("\n👋 程序退出")
        exit(0)

# 获取查询
query = get_user_input()
print(f"🔍 处理查询: {query}")

# Part6

try:
    print("📚 正在检索相关文档...")
    doc_node_list = retriever(query=query)
    print(f"✅ 找到 {len(doc_node_list)} 个相关文档片段")

    print("🤔 正在生成回答...")
    res = llm({
        "query": query,
        "context_str": "".join([node.get_content() for node in doc_node_list]),
    })

    # Part7

    print(f"\n💬 回答: {res}")

except Exception as e:
    print(f"❌ 处理过程中出现错误: {e}")
    print("💡 请检查:")
    print("   1. 文档路径是否正确")
    print("   2. 网络连接是否正常")
    print("   3. API Key 是否有效")